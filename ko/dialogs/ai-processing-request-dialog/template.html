<foquz-dialog params="ref: modal, dialogWrapper: $component">
  <foquz-dialog-header> </foquz-dialog-header>

  <!-- ko if: view() === 'request' -->
  <div class="foquz-dialog__body ai-processing-request-dialog__body">
    <div class="ai-processing-request-dialog__info">
      <p class="ai-processing-request-dialog__intro">
        Для использования функции автоматического определения тональности и
        тегов в открытых ответах нужно<br />
        подключить
        <strong style="font-weight: 500"
          >модуль искусственного интеллекта</strong
        >.
      </p>
      <ul class="ai-processing-request-dialog__list">
        <span class="ai-processing-request-dialog__list-title"
          >Модуль ИИ позволяет:</span
        >
        <li class="ai-processing-request-dialog__list-item">
          автоматически определять эмоциональную окраску ответов (позитивная,
          нейтральная, негативная);
        </li>
        <li class="ai-processing-request-dialog__list-item">
          присваивать релевантные теги для категоризации ответов;
        </li>
        <li class="ai-processing-request-dialog__list-item">
          значительно ускорить анализ больших объемов текстовых данных.
        </li>
      </ul>
      <p class="ai-processing-request-dialog__intro">
        Для подключения модуля ИИ свяжитесь с нами любым удобным способом:
      </p>
      <div class="ai-processing-request-dialog__contacts">
        <div
          class="ai-processing-request-dialog__contacts-item"
          data-bind="click: openChat"
        >
          <div class="ai-processing-request-dialog__contacts-item-icon">
            <svg
              width="19"
              height="18"
              viewBox="0 0 19 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15.2764 1H3.27637C2.17324 1 1.27637 1.91342 1.27637 3.0369V11.1852C1.27637 12.3087 2.17324 13.2221 3.27637 13.2221H6.61001V16.6171C6.61001 16.929 6.96001 17.1104 7.20689 16.9258L11.9437 13.2221H15.2764C16.3795 13.2221 17.2764 12.3087 17.2764 11.1852V3.0369C17.2764 1.91342 16.3795 1 15.2764 1Z"
                stroke="#A6B1BC"
                stroke-width="2"
              />
            </svg>
          </div>
          <div class="ai-processing-request-dialog__contacts-item-content">
            <span class="ai-processing-request-dialog__contacts-item-text"
              >В чат поддержки</span
            >
          </div>
        </div>
        <a
          class="ai-processing-request-dialog__contacts-item"
          href="tel:88005002637"
        >
          <div class="ai-processing-request-dialog__contacts-item-icon">
            <svg
              width="19"
              height="18"
              viewBox="0 0 19 18"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.20215 1.06348C4.90711 0.86232 5.68227 1.1473 6.06445 1.81348L7.70898 4.67969L7.80078 4.86621C7.98437 5.3097 7.95975 5.80944 7.74805 6.23242L7.07422 7.57617C7.4008 8.0639 8.0096 8.88948 8.53809 9.41797L8.8584 9.73828C9.38648 10.2664 10.2114 10.8744 10.6992 11.2012L12.0439 10.5283C12.4669 10.3166 12.9667 10.292 13.4102 10.4756L13.5967 10.5674L16.4629 12.2119C17.1734 12.6195 17.45 13.4742 17.166 14.2139C17.0749 14.4513 16.9548 14.7458 16.8252 15.0166C16.7161 15.2445 16.5352 15.5996 16.2949 15.8398L15.8271 16.3076C15.1493 16.9855 14.2018 17.0635 13.4062 16.9639C12.5833 16.8608 11.6815 16.5431 10.8076 16.1299C9.05502 15.3011 7.18705 13.9771 5.90332 12.6934L5.58301 12.373C4.29928 11.0893 2.97531 9.22134 2.14648 7.46875C1.73324 6.59491 1.41552 5.69304 1.3125 4.87012C1.21291 4.07456 1.29088 3.12709 1.96875 2.44922L2.43652 1.98145C2.67676 1.74121 3.03185 1.56023 3.25977 1.45117C3.53056 1.3216 3.82512 1.20149 4.0625 1.11035L4.20215 1.06348Z"
                stroke="#A6B1BC"
                stroke-width="2"
              />
            </svg>
          </div>
          <div class="ai-processing-request-dialog__contacts-item-content">
            <span class="ai-processing-request-dialog__contacts-item-text"
              >8 800 500 26 37</span
            >
          </div>
        </a>
        <a
          class="ai-processing-request-dialog__contacts-item"
          href="mailto:<EMAIL>"
        >
          <div class="ai-processing-request-dialog__contacts-item-icon">
            <svg
              width="18"
              height="14"
              viewBox="0 0 18 14"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M17 2.5C17 1.675 16.28 1 15.4 1H2.6C1.72 1 1 1.675 1 2.5M17 2.5V11.5C17 12.325 16.28 13 15.4 13H2.6C1.72 13 1 12.325 1 11.5V2.5M17 2.5L9 7.75L1 2.5"
                stroke="#A6B1BC"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div class="ai-processing-request-dialog__contacts-item-content">
            <span class="ai-processing-request-dialog__contacts-item-text"
              ><EMAIL></span
            >
          </div>
        </a>
      </div>
    </div>

    <div class="ai-processing-request-dialog__form">
      <div class="ai-processing-request-dialog__form-title">
        Или оставьте номер телефона, мы сами свяжемся с вами:
      </div>
      <div class="form-group mb-0">
        <input
          type="text"
          class="form-control"
          placeholder="+7 (___) ___ ____"
          data-bind="phoneMask, textInput: phone, css: { 'is-valid': formControlSuccessStateMatcher(phone), 'is-invalid': formControlErrorStateMatcher(phone) }"
        />
        <!-- ko template: { foreach: formControlErrorStateMatcher(phone), afterAdd: fadeAfterAddFactory(200), beforeRemove: fadeBeforeRemoveFactory(200) } -->
        <div class="form-error" data-bind="text: $parent.phone.error()"></div>
        <!-- /ko -->
      </div>
    </div>
  </div>
  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() { $dialog.hide('close'); }"
      >
        Закрыть
      </button>
      <button type="button" class="f-btn" data-bind="click: submit">
        Отправить заявку
      </button>
    </div>
  </div>
  <!-- /ko -->

  <!-- ko if: view() === 'direct-to-poll-settings' -->
  <div class="foquz-dialog__body ai-processing-request-dialog__body ai-processing-request-dialog__body--poll-settings">
    <div class="ai-processing-request-dialog__info ai-processing-request-dialog__info--poll-settings">
      <p class="mb-10p">
        Для использования функции автоматического определения тональности и
        тегов в открытых ответах нужно включить в настройках опроса тумблер
        «<strong class="bold">Определение тональности и тегов для открытого ответа с помощью
        искусственного интеллекта</strong>»
      </p>
      <a
        class="ai-processing-request-dialog__link d-block f-fs-1-5 mb-10p f-color-primary semibold"
        target="_blank"
        data-bind="attr: { href: `/foquz/foquz-poll/settings?id=${pollId}&tab=processingAnswers` }"
      >
        Перейти в «Настройки» опроса
      </a>
      <ul class="ai-processing-request-dialog__list">
        <span class="ai-processing-request-dialog__list-title"
          >Модуль ИИ позволяет:</span
        >
        <li class="ai-processing-request-dialog__list-item">
          автоматически определять эмоциональную окраску ответов (позитивная,
          нейтральная, негативная);
        </li>
        <li class="ai-processing-request-dialog__list-item">
          присваивать релевантные теги для категоризации ответов;
        </li>
        <li class="ai-processing-request-dialog__list-item">
          значительно ускорить анализ больших объемов текстовых данных.
        </li>
      </ul>
    </div>
  </div>
  <div class="foquz-dialog__footer ai-processing-request-dialog__footer ai-processing-request-dialog__footer--poll-settings">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() { $dialog.hide('close'); }"
      >
        Закрыть
      </button>
    </div>
  </div>
  <!-- /ko -->
</foquz-dialog>
