import { DialogWrapper } from 'Dialogs/wrapper';
import { DialogsModule } from "Utils/dialogs-module";
import { copyToClipboard } from "@/utils/copy-to-clipboard";
import { TagsDirectory } from "@/utils/directory/tags";
import { commonFormControlErrorStateMatcher } from "Utils/validation/state-matchers";

export class ViewModel extends DialogWrapper {
  constructor(params, element) {
    super(params, element);

    DialogsModule(this);

    this.readonly = ko.observable(!!params.readonly);
    this.title = ko.observable(params.title || 'Обработка комментария с помощью ИИ');

    this.showSentiment = 'showSentiment' in params ? params.showSentiment : true;
    this.showTagging = 'showTagging' in params ? params.showTagging : true;

    this.maxSentimentPromptLength = 65000
    this.maxTaggingPromptLength = 65000

    // External bindings (observables or plain values)
    this.ext = {
      sentimentEnabled: params.sentimentEnabled,
      sentimentPrompt: params.sentimentPrompt,
      taggingEnabled: params.taggingEnabled,
      taggingPrompt: params.taggingPrompt,
      tags: params.tags,
      questionId: params.questionId,
    };

    this.sentimentEnabled = ko.observable(false);
    this.sentimentPrompt = ko.observable("").extend({
      validation: {
        validator: (value) => {
          if (!this.sentimentEnabled()) return true;
          return value && value.trim().length > 0;
        },
        message: 'Обязательное поле'
      }
    });

    this.taggingEnabled = ko.observable(false);
    this.taggingPrompt = ko.observable("").extend({
      validation: {
        validator: (value) => {
          if (!this.taggingEnabled()) return true;
          return value && value.trim().length > 0;
        },
        message: 'Обязательное поле'
      }
    });
    this.tags = ko.observableArray([]);
    // Directory for available tags
    this.directories = {
      tags: new TagsDirectory(),
    };
    // Load tags at init
    this.directories.tags.load();
    this.tagsList = this.directories.tags.data;

    // Preselected tags can be passed via params.tags
    this._hasInitialTagsFromParams = 'tags' in params;
    if (this._hasInitialTagsFromParams) {
      const initial = ko.isObservable(params.tags) ? params.tags() : (params.tags || []);
      this.tags(initial.slice());
    }



    this.sentimentPromptCopied = ko.observable(false);
    this.taggingPromptCopied = ko.observable(false);

    // Error toast for tag operations
    this.errorToast = ko.observable(false);
    this.errorMessage = ko.observable('Произошла ошибка. Попробуйте ещё раз');

    this.isSubmitted = ko.observable(false);
    this.formControlErrorStateMatcher = commonFormControlErrorStateMatcher(this.isSubmitted);

    this.sentimentInvalid = ko.computed(() => this.isSubmitted() && this.sentimentEnabled() && !this.sentimentPrompt().trim());
    this.taggingInvalid = ko.computed(() => this.isSubmitted() && this.taggingEnabled() && !this.taggingPrompt().trim());

    this._initialized = false;
    this.initFromParams();

    this.onEnter = () => {
      this.apply();
    };
  }

  initFromParams() {
    const get = (v, def) => (ko.isObservable(v) ? v() : (typeof v !== 'undefined' ? v : def));

    this.sentimentEnabled(!!get(this.ext.sentimentEnabled, false));
    this.sentimentPrompt(get(this.ext.sentimentPrompt, "") || "");

    this.taggingEnabled(!!get(this.ext.taggingEnabled, false));
    this.taggingPrompt(get(this.ext.taggingPrompt, "") || "");
    const tags = this._hasInitialTagsFromParams
      ? (ko.isObservable(this.ext.tags) ? this.ext.tags() : (this.ext.tags || []))
      : get(this.ext.tags, []);
    this.tags(tags ? [...tags] : []);

    if (!this._initialized) {
      this._initialSentimentEnabled = this.sentimentEnabled();
      this._initialSentimentPrompt = this.sentimentPrompt();
      this._initialTaggingEnabled = this.taggingEnabled();
      this._initialTaggingPrompt = this.taggingPrompt();
      this._initialTags = this.tags().slice();
      this._initialized = true;
    }
  }

  reset() {
    this.sentimentEnabled(this._initialSentimentEnabled);
    this.sentimentPrompt(this._initialSentimentPrompt);
    this.taggingEnabled(this._initialTaggingEnabled);
    this.taggingPrompt(this._initialTaggingPrompt);
    this.tags(this._initialTags.slice());
  }


  validate() {
    if (this.sentimentEnabled() && !this.sentimentPrompt().trim()) return false;
    if (this.taggingEnabled()) {
      if (!this.taggingPrompt().trim()) return false;
      if (!this.tags() || !this.tags().length) return false;
    }
    return true;
  }

  cancel() {
    this.reset();
    this.hide('cancel');
  }

  apply() {
    this.isSubmitted(true);
    if (!this.validate()) return;

    // Push changes back to external observables if provided
    if (ko.isObservable(this.ext.sentimentEnabled)) this.ext.sentimentEnabled(this.sentimentEnabled());
    if (ko.isObservable(this.ext.sentimentPrompt)) this.ext.sentimentPrompt(this.sentimentPrompt().trim());

    if (ko.isObservable(this.ext.taggingEnabled)) this.ext.taggingEnabled(this.taggingEnabled());
    if (ko.isObservable(this.ext.taggingPrompt)) this.ext.taggingPrompt(this.taggingPrompt().trim());
    if (ko.isObservable(this.ext.tags)) this.ext.tags(this.tags());

    this.hide('apply');
  }
}
