<sidesheet params="ref: modal, dialogWrapper: $component">
  <div class="foquz-dialog__header">
    <div class="container">
      <h2 class="foquz-dialog__title" data-bind="text: title"></h2>
    </div>
  </div>

  <div class="foquz-dialog__body ai-field-processing-sidesheet__body pt-40p">
    <div class="foquz-dialog__scroll" data-bind="nativeScrollbar">
      <div class="container pb-4" data-tooltip-container>
        <success-message
          class="foquz-success-message--error"
          params="show: errorToast, text: errorMessage, showCross: true, delay: 3000, centered: true"></success-message>
        <!-- Sentiment -->
        <!-- ko if: showSentiment -->
        <div class="ai-field-processing-sidesheet__section">
          <fc-switch
            params="checked: sentimentEnabled, disabled: readonly, label: 'Определение тональности с помощью ИИ'"
          ></fc-switch>

          <!-- ko template: { foreach: templateIf(sentimentEnabled(), $data), afterAdd: slideAfterAddFactory(300), beforeRemove: slideBeforeRemoveFactory(200) } -->
          <div class="ai-field-processing-sidesheet__block">
            <div class="ai-field-processing-sidesheet__block-inner">
              <div class="ai-field-processing-sidesheet__hint">
                ИИ будет анализировать комментарии и определять их эмоциональную
                окраску. Используйте в промте три типа тональности: негативный,
                нейтральный и позитивный.
              </div>

              <div class="form-group">
                <label class="form-label" for="sentiment-prompt-textarea"
                  >Промт для определения тональности</label
                >

                <div
                  class="ai-field-processing-sidesheet__textarea ai-field-processing-sidesheet__textarea--sentiment-prompt"
                  id="sentiment-prompt"
                >
                  <scrollable-textarea
                    params="
                      value: sentimentPrompt,
                      disabled: readonly,
                      maxlength: maxSentimentPromptLength,
                      isInvalid: sentimentInvalid,
                      copyable: true,
                      maxHeight: 250
                    "
                  ></scrollable-textarea>
                </div>
                <validation-feedback
                  params="show: formControlErrorStateMatcher(sentimentPrompt), text: sentimentPrompt.error"
                ></validation-feedback>
                
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->

        <!-- Tagging -->
        <!-- ko if: showTagging -->
        <div class="ai-field-processing-sidesheet__section">
          <fc-switch
            params="checked: taggingEnabled, disabled: readonly, label: 'Тегирование с помощью ИИ'"
          ></fc-switch>

          <!-- ko template: { foreach: templateIf(taggingEnabled(), $data), afterAdd: slideAfterAddFactory(300), beforeRemove: slideBeforeRemoveFactory(200) } -->
          <div class="ai-field-processing-sidesheet__block">
            <div class="ai-field-processing-sidesheet__block-inner">
              <div class="ai-field-processing-sidesheet__hint">
                ИИ будет автоматически присваивать выбранные теги комментариям
                респондентов. Используйте в промте выбранные теги для точной
                категоризации ответов.
              </div>

              <div class="form-group">
                <label class="form-label">Теги</label>
                <div class="ai-field-processing-sidesheet__tags">
                  <!-- ko if: directories.tags.loaded -->
                  <div
                    data-bind="
                      component: {
                        name: 'clients-tag-input',
                        params: {
                          afterAddTag: function() { directories.tags.load(true) },
                          value: tags,
                          list: directories.tags.data,
                          question_id: ext.questionId,
                          onAddTagError: function() { errorToast(true) },
                          onRemoveTagError: function() { errorToast(true) },
                          transport: {
                            add: {
                              url: '/api/tags',
                              method: 'POST',
                              prepare: function(args) {
                                var context = args && args.context || {};
                                return {
                                  contentType: 'application/json; charset=utf-8',
                                  processData: false,
                                  data: JSON.stringify({
                                    textAnswerId: '123lalala',
                                    tag: context.name
                                  })
                                };
                              }
                            },
                            remove: {
                              method: 'DELETE',
                              url: function(context) {
                                const tid = context && context.tag && context.tag.id;
                                return '/api/tags/lala/remove/lala/' + tid;
                              },
                              prepare: function() { return { data: undefined }; }
                            }
                          }
                        }
                      }
                    "
                  ></div>
                  <!-- /ko -->
                  <!-- ko if: isSubmitted() && (!tags() || !tags().length) -->
                  <div class="form-error">Выберите хотя бы один тег</div>
                  <!-- /ko -->
                </div>
              </div>

              <div class="form-group">
                <label class="form-label" for="tagging-prompt-textarea"
                  >Промт для тегирования</label
                >
                <div
                  class="ai-field-processing-sidesheet__textarea ai-field-processing-sidesheet__textarea--tagging-prompt"
                  id="tagging-prompt"
                >
                  <scrollable-textarea
                    params="
                      value: taggingPrompt,
                      disabled: readonly,
                      maxlength: maxTaggingPromptLength,
                      isInvalid: taggingInvalid,
                      copyable: true,
                      maxHeight: 250
                    "
                  ></scrollable-textarea>
                </div>
                <validation-feedback
                  params="show: formControlErrorStateMatcher(taggingPrompt), text: taggingPrompt.error"
                ></validation-feedback>
                
              </div>
            </div>
          </div>
          <!-- /ko -->
        </div>
        <!-- /ko -->
      </div>
    </div>
  </div>

  <div class="foquz-dialog__footer">
    <div class="foquz-dialog__actions">
      <button
        type="button"
        class="f-btn f-btn-link"
        data-bind="click: function() { cancel() }"
      >
        Отменить
      </button>
      <button
        type="button"
        class="f-btn"
        data-bind="click: function() { apply() }"
      >
        Применить
      </button>
    </div>
  </div>
</sidesheet>
