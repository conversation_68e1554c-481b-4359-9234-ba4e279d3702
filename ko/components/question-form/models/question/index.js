/**
 * Модель вопроса
 */

import ee from "event-emitter";
import allOff from "event-emitter/all-off";

import {
  questionTranslator,
  validationTranslator,
} from "@/components/question-form/translator";

const answersFromFormat = "DD.MM.YYYY HH:mm";
let unique = 1;

import { DialogsModule } from "@/utils/dialogs-module";

class Question {
  constructor(controller) {
    ee(this);

    DialogsModule(this);

    this.unique = unique++;

    this.translator = questionTranslator;
    this.isPaidRate = !!window.isPaidRate;
    this.isContactsEnabled = !!window.isContactsEnabled;

    this.pointId = ko.observable(null);
    this.actionsBlocked = ko.observable(false);

    this.subscriptions = [];

    this.controller = controller;
    this.formControlErrorStateMatcher =
      this.controller.formControlErrorStateMatcher;
    this.formControlSuccessStateMatcher =
      this.controller.formControlSuccessStateMatcher;

    this.pollAiEnabled = this.controller.pollAiEnabled;
    this.companyAiEnabled = this.controller.companyAiEnabled;

    this.isBlocked = ko.observable(false);
    this.isFullBlocked = this.controller.isBlocked;

    if (window.CURRENT_USER.watcher) {
      this.isBlocked(true);
      this.isFullBlocked = true;
      this.controller.isBlocked = true;
    }

    this.logic = ko.observable(null);
    this.viewLogic = ko.observable(null);

    this.mode = this.controller.mode;

    this.serverErrors = {
      alias: ko.observable(""),
      name: ko.observable(""),
      description: ko.observable(""),
    };

    this.id = ko.observable(0);
    this.required = ko.observable(true);
    this.alias = ko.observable("");
    this.name = ko.observable("");
    this.description = ko.observable("");
    this.descriptionText = ko.observable("");
    this.subdescription = ko.observable("");
    this.pointName = ko.observable("");

    this.dontShowIfAnswered = ko.observable(false);
    this.answersFrom = ko.observable(moment().format(answersFromFormat));

    this.enableDictionaryConnection = ko.observable(false);
    this.dictionaryElementId = ko.observable(null);
    this.dictionaryElementName = ko.observable(null);
    this.enableDictionaryConnection.subscribe(v => {
      if (!v) {
        this.dictionaryElementId(null);
      }
    });

    this.resetAnswers = () => {
      let now = moment().format(answersFromFormat);
      this.answersFrom(now);
    };

    this.subscriptions.push(
      this.dontShowIfAnswered.subscribe((v) => {
        if (v) {
          if (!this.answersFrom()) this.resetAnswers();
        }
      }),
      this.alias.subscribe((_) => {
        this.serverErrors.alias("");
      }),
      this.name.subscribe((_) => {
        this.serverErrors.name("");
      }),
      this.description.subscribe((_) => {
        this.serverErrors.description("");
      })
    );

    this.alias.extend({
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.alias(),
        message: () => this.serverErrors.alias(),
      },
    });

    this.name.extend({
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.name(),
        message: () => this.serverErrors.name(),
      },
    });

    this.description.extend({
      required: {
        message: () => validationTranslator.t("Обязательное поле")(),
      },
      validation: {
        validator: () => false,
        onlyIf: () => !!this.serverErrors.description(),
        message: () => this.serverErrors.description(),
      },
    });

    this.isAuto = false;
    this.isSystem = false;
    this._isSystem = ko.observable(false);
    this.withPoints = this.controller.withPoints;
    this.isSource = false;
    this.isUpdated = ko.observable(false);
    this.isTmp = ko.observable(true);
    this.countAnswers = 0;
    this.usesDictionaryElements = ko.observable(false);

    this.dictionaryBadgeTooltipText = ko.computed(() => {
      if (!controller.question()) {
        return '';
      }
      return `
        ${controller.question().dictionaryElementId() ? `Связка вопроса с элементом ${this.dictionaryElementName()} справочника ${POLL.dictionary_name}` : ''}
        ${controller.question().usesDictionaryElements() ? `${controller.question().dictionaryElementId() ? '.' : ''} В вопросе есть варианты, связанные с элементами справочника ${POLL.dictionary_name}` : ''}
      `;
    }, this);

    [
      this.required,
      this.alias,
      this.name,
      this.description,
      this.subdescription,
      this.dictionaryElementId,
    ].forEach((f) => {
      this.subscriptions.push(
        f.subscribe((v) => {
          this.onChange();
        })
      );
    });
  }

  block() {
    this.actionsBlocked(true);
  }

  dispose() {
    allOff(this);
    this.subscriptions.forEach((s) => s.dispose());
  }

  get hasMainValidation() {
    return true;
  }

  get needRequiredSetting() {
    return true;
  }

  tryChangeBlockedParam() {
    if (typeof this.controller.tryChangeBlockedParam === "function") {
      this.controller.tryChangeBlockedParam(this);
    }
  }

  noDonorsInfo() {
    if (typeof this.controller.noDonorsInfo === "function") {
      this.controller.noDonorsInfo();
    }
  }

  onChange() {
    this.controller.onChange(this);
  }

  getCommonData() {
    let data = {};

    data.id = this.id();

    data.type = this.type;
    data.alias = this.alias();
    data.name = this.name();
    data.description = this.description();
    data.subdescription = this.subdescription();
    data.required = this.required();

    data.dontShowIfAnswered = this.dontShowIfAnswered();
    data.answersFrom = this.answersFrom();

    data.pointId = this.pointId();
    data.dictionaryElementId = this.dictionaryElementId();

    return data;
  }

  getLogicVariants(variants) {
    return variants;
  }

  updateCommonData(data) {
    this.id(data.id);
    this.isAuto = data.isAuto;

    this.logic(data.questionLogic);
    this.viewLogic(data.questionViewLogic);

    this.isSystem = data.isSystem;
    this._isSystem(data.isSystem);
    this.isSource = data.isSource;
    this.countAnswers = data.countAnswers;

    this.isUpdated(data.isUpdated);
    this.isTmp(data.isTmp);

    let isBlocked = false;
    if (this.isSystem) isBlocked = true;
    if (this.isAuto) isBlocked = true;
    if (data.pointId) isBlocked = true;
    if (this.countAnswers > 0) isBlocked = true;
    this.isBlocked(isBlocked);

    this.required(!!data.required);
    this.alias(data.alias || "");
    this.pointName(data.pointName || "");
    this.name(data.name || "");
    this.description(data.description || "");
    this.descriptionText(data.descriptionText || "");
    this.subdescription(data.subdescription || "");

    this.pointId(data.pointId);

    if (
      data.answersFrom &&
      moment(data.answersFrom, answersFromFormat).isValid()
    ) {
      this.answersFrom(data.answersFrom);
    } else {
      this.answersFrom(moment().format(answersFromFormat));
    }

    this.dontShowIfAnswered(!!data.dontShowIfAnswered);
    this.dictionaryElementId(data.dictionary_element_id ? data.dictionary_element_id : null);
    this.dictionaryElementName(data.dictionary_element_name ? data.dictionary_element_name : null);
    this.enableDictionaryConnection(!!data.dictionary_element_id);
  }

  updateData(data) {
    this.updateCommonData(data);
  }

  getData() {
    return this.getCommonData();
  }

  isValid() {
    return this.defaultValidation();
  }

  defaultValidation() {
    if (!this.alias.isValid()) return false;
    if (!this.description.isValid()) return false;
    return true;
  }

  setErrors(errors) {
    Object.keys(this.serverErrors).forEach((key) => {
      if (key in errors) {
        this.serverErrors[key](errors[key]);
      }
    });
  }
}

export default Question;
