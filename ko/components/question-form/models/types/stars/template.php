<template id="question-form-template-stars">
  <!-- ko let: { $translator: question.translator } -->

  <div class="row">
    <div class="col-6">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Цвет звезд')"></label>
        <div class="input-group" data-bind="colorPicker">
          <div class="input-group-prepend  poll-design__color-preview js-color-preview" data-bind="attr: {'data-backup': question.color}"></div>
          <input required class="form-control" data-bind="textInput: question.color, disable: question.isFullBlocked" maxlength="7" data-is-hex="true">
        </div>
      </div>
    </div>

    <div class="col-6">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Количество звезд')"></label>

        <div class="input-group slider-input-group" data-bind="click: function() {
          if (question.isFullBlocked) return false;
          if (question.isBlocked()) {
            question.tryChangeBlockedParam();
          }
          else return true;
        }">
          <div class="form-control" data-bind="slider, sliderMin: 2, sliderMax: 10, sliderValue: question.count, disabled: question.isBlocked() || question.isFullBlocked"></div>

          <div class="input-group-append" data-bind="style: {
            opacity:question.isBlocked() ? 0.5 : 1
          }">
            <span class="input-group-text" data-bind="text: question.count"></span>
          </div>
        </div>
      </div>
    </div>

    <div class="col-12">
      <div class="form-group">
        <label class="form-label" data-bind="text: $translator.t('Размер')"></label>
        <div class="f-fs-1 f-color-service mb-3" data-bind="text: $translator.t('Для количества звезд более 5 доступен только средний и мелкий размер. На экранах с небольшим разрешением мелкий размер будет отображаться как средний.')">

        </div>
        <div class="d-flex align-items-end">
          <span class="mr-3" data-bind="css: {
              'f-color-primary': question.size() == 'lg',
              'f-color-light': question.size() != 'lg',
              'cursor-pointer': !question.isFullBlocked && question.count() <= 5,
              'cursor-not-allowed': question.isFullBlocked || question.count() > 5,
            },
            style: {
              opacity: question.count() > 5 ? 0.5 : 1
            },
            click: function() {
              if (question.isFullBlocked) return false;
              if (question.count() <= 5) {
                question.size('lg');
              }
            }">
            <svg width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M38.918 45.9376L38.9189 45.9468L38.92 45.956C38.9848 46.4994 38.6114 46.9367 38.1697 46.9929L38.1696 46.9929C38.0025 47.0142 37.8337 46.9826 37.6831 46.9L37.6831 46.9L37.6748 46.8955L24.7028 39.9356L24.23 39.6819L23.7572 39.9356L10.7862 46.8955L10.7862 46.8955L10.7787 46.8996C10.615 46.9893 10.4318 47.0189 10.2554 46.9883C9.81269 46.9104 9.45858 46.4531 9.54629 45.9137L9.54689 45.9099L11.9184 30.9683L11.997 30.4727L11.6468 30.1135L1.26748 19.4664C1.26741 19.4664 1.26734 19.4663 1.26727 19.4662C1.13316 19.3282 1.04168 19.1459 1.01164 18.9443L1.01139 18.9426C0.929947 18.4024 1.28848 17.9512 1.73099 17.879C1.73137 17.879 1.73174 17.8789 1.73211 17.8788L16.1771 15.5911L16.6934 15.5093L16.9209 15.0388L23.4695 1.49864C23.4696 1.49836 23.4698 1.49807 23.4699 1.49779C23.5584 1.3162 23.6964 1.17678 23.8583 1.09213C24.2562 0.885177 24.766 1.03516 24.9909 1.49745C24.9911 1.49773 24.9912 1.49801 24.9914 1.49829L31.5401 15.0388L31.7677 15.5093L32.2839 15.5911L46.7293 17.8789C46.7295 17.879 46.7298 17.879 46.73 17.879C46.9024 17.907 47.0672 17.9919 47.1994 18.1301C47.5506 18.4979 47.547 19.1036 47.1946 19.4654C47.1945 19.4654 47.1945 19.4655 47.1944 19.4655L36.8142 30.1135L36.464 30.4727L36.5426 30.9683L38.9141 45.9099L38.9141 45.91L38.9155 45.9184C38.9161 45.9221 38.9171 45.9289 38.918 45.9376Z" stroke="currentColor" stroke-width="2" />
            </svg>
          </span>

          <span class="cursor-pointer mr-3" data-bind="css: {
              'f-color-primary': question.size() == 'md',
              'f-color-light': question.size() != 'md',
              'cursor-pointer': !question.isFullBlocked,
              'cursor-not-allowed': question.isFullBlocked,
            },
            click: function() {
              if (question.isFullBlocked) return false;
              question.size('md');
            }">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M25.1616 30.6937L25.1615 30.6937C25.1224 30.6987 25.0831 30.6916 25.0465 30.6715L25.0466 30.6715L25.0383 30.667L16.4725 26.0712L15.9997 25.8175L15.5269 26.0712L6.96182 30.6671L6.9618 30.667L6.95429 30.6711C6.91311 30.6937 6.87015 30.6999 6.83019 30.693C6.73112 30.6752 6.60924 30.5552 6.63895 30.3725L6.63954 30.3688L8.20551 20.5024L8.28416 20.0069L7.93392 19.6476L1.08053 12.6175C1.08042 12.6174 1.0803 12.6172 1.08019 12.6171C1.0418 12.5775 1.01341 12.5227 1.00398 12.4594L1.00372 12.4577C0.976148 12.2748 1.09886 12.1578 1.19709 12.1413C1.19739 12.1413 1.19769 12.1412 1.19798 12.1412L10.7353 10.6307L11.2516 10.5489L11.4791 10.0784L15.803 1.13807C15.8031 1.1379 15.8032 1.13774 15.8033 1.13758C15.8305 1.08175 15.8703 1.04388 15.9113 1.02236C15.9981 0.977419 16.1287 0.997687 16.1967 1.13738C16.1968 1.13749 16.1968 1.1376 16.1969 1.13771L20.521 10.0784L20.7485 10.5489L21.2648 10.6307L30.8025 12.1413C30.8028 12.1413 30.8031 12.1414 30.8034 12.1414C30.8425 12.1479 30.8844 12.1678 30.9215 12.2065C31.0274 12.3175 31.0252 12.5089 30.9206 12.6164C30.9206 12.6165 30.9205 12.6165 30.9205 12.6166L24.0662 19.6476L23.716 20.0069L23.7946 20.5024L24.7822 20.3457C23.7946 20.5024 23.7946 20.5024 23.7946 20.5025L23.7947 20.5028L23.7949 20.5042L23.7957 20.5096L23.7991 20.5308L23.8122 20.6136L23.8621 20.928L24.0402 22.0501L24.5806 25.4565C24.6763 26.06 24.7711 26.6582 24.86 27.2185C25.1415 28.9941 25.3628 30.3896 25.3606 30.3678L25.3615 30.377L25.3626 30.3862C25.3846 30.5708 25.2574 30.6815 25.1616 30.6937Z" stroke="currentColor" stroke-width="2" />
            </svg>
          </span>

          <span class="cursor-pointer" data-bind="css: {
              'f-color-primary': question.size() == 'sm',
              'f-color-light': question.size() != 'sm',
              'cursor-pointer': !question.isFullBlocked,
              'cursor-not-allowed': question.isFullBlocked,
            },
            click: function() {
              if (question.isFullBlocked) return false;
              question.size('sm');
            }">
            <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M13.533 15.2612C13.6143 15.7744 13.6873 16.2346 13.7405 16.5694L9.47264 14.2795L8.99984 14.0259L8.52705 14.2796L4.25911 16.5696L5.04769 11.6012L5.12634 11.1057L4.7761 10.7464L1.29373 7.17421L6.10705 6.41187L6.62329 6.33011L6.85086 5.85958L9.00003 1.41587L11.1492 5.85958L11.3768 6.33011L11.893 6.41187L16.7063 7.17421L13.224 10.7464L12.8737 11.1056L12.9524 11.6012L13.94 11.4444L12.9524 11.6012L12.9524 11.6014L12.9525 11.6022L12.953 11.6052L12.9549 11.6171L12.9623 11.6637L12.9903 11.8405L13.0905 12.4717L13.3945 14.3878C13.4419 14.6869 13.4887 14.9819 13.533 15.2612Z" stroke="currentColor" stroke-width="2" />
            </svg>
          </span>


        </div>
      </div>
    </div>

    <div class="col-12">
      <div class="mb-1">
        <label class="form-label">
          <span data-bind="text: $translator.t('Метки')"></span>
          <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipText: $translator.t('Метки')"></button>
        </label>

        <div class="d-flex flex-wrap mx-n1">
          <!-- ko foreach: question.visibleLabels -->
          <div class="mb-3 stars-question-label">
            <input type="text" class="form-control mb-1" data-bind="textInput: value, disable: question.isFullBlocked" maxlength="150">
            <div data-bind="text: label" class="text-center"></div>
          </div>
          <!-- /ko -->
        </div>
      </div>


      <div class="row">
        <div class="col col-6">
          <fc-check params="
              checked: question.showNumbers,
              label: 'Показывать числа над звездами', 
              hint: 'Показывать числа над звездами', disabled: question.isFullBlocked"></fc-check>
        </div>
        <!-- ko template: {
           foreach: templateIf(question.count() <= 5, $data),
           afterAdd: fadeAfterAddFactory(400),
           beforeRemove: fadeBeforeRemoveFactory(400)
        } -->
        <div class="col col-6">
          <fc-check params="
              checked: question.showLabels,
              label: 'Всегда отображать метки', 
              hint: 'Метки всегда можно отображать для шкалы не более 5 звёзд', disabled: question.isFullBlocked"></fc-check>
        </div>
        <!-- /ko -->

      </div>

    </div>
  </div>

  <hr class="mx-0">

  <div>
    <div class="form-group">
      <fc-switch params="checked: question.skip, label: $translator.t('Пропуск оценки'), disabled: question.isFullBlocked"></fc-switch>
    </div>

    <!-- ko template: {
       foreach: templateIf(question.skip(), $data),
       afterAdd: slideAfterAddFactory(400),
       beforeRemove: slideBeforeRemoveFactory(400)
    } -->
    <div class="form-group">
      <fc-label params="text: $translator.t('Текст'), hint: $translator.t('Текст')"></fc-label>
      <fc-input
        params="
          value: question.skipText,
          counter: true,
          maxlength: 125,
          placeholder: $translator.t('Не готов(а) оценить'),
          disabled: question.isFullBlocked,
        "
      ></fc-input>
    </div>

    <!-- /ko -->
  </div>

  <hr class="mx-0">

  <!-- ko template: {
    name: 'gallery-question-gallery-template',
    data: {
      question: question,
    }
  } -->
  <!-- /ko -->

  <hr>

  <div class="row pb-1">
    <div class="col">
      <div class="pt-1">

        <switch class="mb-0" params="checked: question.clarifyingQuestionEnabled, disabled: question.isBlocked() || question.isFullBlocked" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()) question.tryChangeBlockedParam();
                    else return true;
                }">
          <span data-bind="text: $translator.t('Уточняющий вопрос')"></span>
        </switch>
      </div>
    </div>
    <div class="col">
      <div class="pt-1">
        <switch class="mb-0" params="checked: question.commentEnabled, disabled: question.isFullBlocked || (question.isBlocked() && question.clarifyingQuestionEnabled())" data-bind="click: function() {
                    if (question.isFullBlocked) return false;
                    if (question.isBlocked()  && question.clarifyingQuestionEnabled()) question.tryChangeBlockedParam();
                    else return true;
                }">
          <span data-bind="text: $translator.t('Комментарий')"></span>
        </switch>

      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: question.clarifyingQuestionEnabled,
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="row pt-4">
    <div class="col">
      <div class="f-check">
        <input type="checkbox" id="for-all-rate" class="f-check-input" data-bind="checked: question.clarifyingQuestionForAllRates, disable: question.isFullBlocked">
        <label for="for-all-rate" class="f-check-label" data-bind="text: $translator.t('Для всех оценок')"></label>
      </div>
    </div>
    <div class="col">
      <div class="f-check">
        <input
          type="checkbox"
          id="extra-required"
          class="f-check-input"
          data-bind="
            checked: question.clarifyingQuestionIsRequired,
            disable: question.isFullBlocked,
          "
        />
        <label for="extra-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
    </div>
  </div>
  <!-- ko template: {
    foreach: templateIf(!question.clarifyingQuestionForAllRates(), $data),
    afterAdd: fadeAfterAddFactory(200),
    beforeRemove: fadeBeforeRemoveFactory(200)
  } -->
  <div class="row pt-4">
    <div class="col mb-4">
      <fc-label params="text: 'Для оценок'"></fc-label>
      <fc-range class="clarifying-answer-settings__rates-range" params="
          value: question.clarifyingQuestionForRates,
          min: 1,
          max: question.count,
          tooltips: true,
          disabled: question.isFullBlocked,
        "></fc-range>
    </div>
  </div>
  <!-- /ko -->
  <!-- /ko -->

  <!-- Уточняющий вопрос -->
  <!-- ko template: {
        foreach: templateIf(question.clarifyingQuestionEnabled(), $data),
        afterAdd: slideAfterAddFactory(200, 150),
        beforeRemove: slideBeforeRemoveFactory(150)
    } -->
  <div class="form-group mt-3">
    <label class="form-label" for="clarifyingQuestion" data-bind="text: $translator.t('Уточняющий вопрос')"></label>

    <button class="btn-question" tabindex="10" data-bind="tooltip, tooltipPlacement: 'top', tooltipText: $translator.t('Можно добавить вопрос, который дополнительно будет задан респонденту для уточнения его ответа. Уточняющий вопрос должен быть всегда с вариантами ответов')" type="button">
    </button>

    <div class="chars-counter chars-counter--type_textarea" data-bind="charsCounter, charsCounterCount: question.clarifyingQuestionText().length">
      <textarea class="form-control" data-bind="
          textInput: question.clarifyingQuestionText,
          css: {
              'is-invalid': controller.formControlErrorStateMatcher(question.clarifyingQuestionText),
              'is-valid': controller.formControlSuccessStateMatcher(question.clarifyingQuestionText)
          },
          autosizeTextarea,
          disable: question.isFullBlocked" id="clarifyingQuestion" maxlength="500">
      </textarea>


      <div class="chars-counter__value"></div>

    </div>

    <!-- ko if: controller.formControlErrorStateMatcher(question.clarifyingQuestionText) -->
    <div class="form-error" data-bind="text: question.clarifyingQuestionText.error()"></div>
    <!-- /ko -->
  </div>

  <div class="">
    <question-form-variants-list params="controller: question.clarifyingQuestionController, disabled: question.isFullBlocked">
    </question-form-variants-list>
  </div>
  <!-- /ko -->
  <!-- /Уточняющий вопрос -->


  <!-- Комментарий -->
  <!-- ko template: {
      foreach: templateIf(question.commentEnabled(), $data),
      afterAdd: slideAfterAddFactory(200, 200),
      beforeRemove: slideBeforeRemoveFactory(150)
  } -->
  <div class="row pt-4">
    <div class="col">
      <div class="f-check">
        <input
          type="checkbox"
          id="comment-required"
          class="f-check-input"
          data-bind="
            checked: question.commentIsRequired,
            disable: question.isFullBlocked,
            event: { change: function() { 
              question.updateCommentRequired() } }
          "
        />
        <label for="comment-required" class="f-check-label" data-bind="text: $translator.t('Обязательный')"></label>
      </div>
    </div>
  </div>
  <div class="mt-4">
    <div class="form-group">
      <fc-label params="text: 'Наименование поля', hint: 'Наименование поля'"></fc-label>
      <fc-input params="value: question.commentLabel, maxlength: 120, counter: true, placeholder: 'Ваш комментарий', disabled: question.isFullBlocked"></fc-input>
    </div>

    <!-- ko component: {
        name: 'text-field',
        params: {
            controller: question.commentField,
            intervalText: $translator.t('Кол-во символов в комментарии'),
            disabled: question.isFullBlocked
        }
    } -->
    <!-- /ko -->

    <div class="ai-processing-settings mt-3 pb-2">
      <ai-processing-field-sidesheet-trigger params="
        label: 'Тональность и теги через модуль ИИ',
        isActive: question.aiCommentFieldProcessingActive,
        companyAiEnabled: question.companyAiEnabled,
        pollAiEnabled: question.pollAiEnabled,
        onSidesheetTrigger: function() { question.openAiFieldCommentSidesheet() }
      "></ai-processing-field-sidesheet-trigger>
        <div class="ai-processing-settings__info f-fs-1 f-color-service mt-10p">
        Автоматический анализ комментариев с помощью ИИ — определение тональности и присвоение тегов для категоризации
      </div>
    </div>
  </div>
  <!-- /ko -->
  <!-- /Комментарий -->

  <!-- /ko -->
</template>
