import "Components/input/color-picker";

import GalleryQuestion from "../gallery";
import { STARS_QUESTION } from "Data/question-types";

import { VariantsController } from "../../../controllers/variants-controller";
import { Translator } from "@/utils/translate";
import { declOfNum } from "@/utils/string/decl-of-num";

const QuestionTranslator = Translator("question");
const ValidationTranslator = Translator("validation");

const starDefaultColor = "#F8CD1C";

class StarsQuestion extends GalleryQuestion {
  constructor(controller, config = {}) {
    config.galleryConfig = {
      freeRemove: true,
    };

    super(controller, config);

    this.translator = QuestionTranslator;

    this.type = STARS_QUESTION;

    this.color = ko.observable(starDefaultColor);
    this.count = ko.observable(5);

    this.size = ko.observable("md");

    this.count.subscribe((v) => {
      if (v > 5 && this.size() == "lg") {
        this.size("md");
      }
    });

    this.labels = Array(10)
      .fill()
      .map((_, i) => {
        let label = utils.declOfNum(i + 1, ["звезда", "звезды", "звезд"]);

        return {
          value: ko.observable(""),
          label: this.translator.t(`{count} ${label}`, {
            count: i + 1,
          }),
        };
      });

    this.visibleLabels = ko.pureComputed(() => {
      return this.labels.slice(0, this.count());
    });

    this.skip = ko.observable(false);
    this.skipText = ko.observable("");

    this.galleryEnabled = ko.observable(false);

    this.clarifyingQuestionEnabled = ko.observable(false);
    this.clarifyingQuestionText = ko.observable("").extend({
      required: {
        message: () => ValidationTranslator.t("Обязательное поле")(),
        onlyIf: () => this.clarifyingQuestionEnabled(),
      },
    });
    this.clarifyingQuestionController = new VariantsController(
      {
        withCustomAnswer: true,
        withVariantsTypeSelect: true,
        withAddVariantButton: true,
        addFirstVariant: true,
        variantsType: "single",
        withTextAnswer: true,
      },
      this
    );
    this.clarifyingQuestionForAllRates = ko.observable(0);
    this.clarifyingQuestionIsRequired = ko.observable(1);
    this.clarifyingQuestionForRates = ko.observable([1, 5]);

    this.clarifyingQuestionEnabled.subscribe((v) => {
      if (!v) this.clarifyingQuestionForAllRates(false);
      else this.commentEnabled(false);
    });
    this.commentEnabled.subscribe((v) => {
      if (v) this.clarifyingQuestionEnabled(false);
    });

    this.commentIsRequired = ko.observable(null);

    this.showNumbers = ko.observable(false);
    this.showLabels = ko.observable(false);

    this.count.subscribe((v) => {
      if (v > 5) this.showLabels(false);
    });

    [
      this.color,
      this.count,
      this.size,
      ...this.labels.map((l) => l.value),
      this.galleryEnabled,
      this.clarifyingQuestionEnabled,
      this.clarifyingQuestionForAllRates,
      this.clarifyingQuestionIsRequired,
      this.clarifyingQuestionText,
      this.skip,
      this.skipText,
      this.showNumbers,
      this.showLabels,
      this.commentIsRequired,
      this.clarifyingQuestionForRates,
    ].forEach((f) => f.subscribe((v) => this.onChange()));

    [this.clarifyingQuestionController].forEach((c) =>
      c.isChanged.subscribe((v) => this.onChange())
    );

    this.aiCommentSentimentEnabled = ko.observable(false);
    this.aiCommentSentimentPrompt = ko.observable("");
    this.aiCommentTaggingEnabled = ko.observable(false);
    this.aiCommentTaggingPrompt = ko.observable("");
    this.aiCommentTags = ko.observableArray([]);

    this.aiCommentFieldProcessingActive = ko.pureComputed(() => {
      return !!(this.aiCommentSentimentEnabled() || this.aiCommentTaggingEnabled());
    });

  }

  updateCommentRequired() {
    this.commentField.updateReuired(this.commentIsRequired())
  }

  getLogicVariants(variants) {
    let _variants = [...variants];
    _variants.sort();
    return _variants.map((v) => {
      const word = declOfNum(v, ["звезда", "звезды", "звезд"]);
      return `${v} ${word}`;
    });
  }

  updateData(data) {
    let comment = data.comment;
    super.updateData({
      ...data,
      galleryCommentEnabled: comment.enabled,
      galleryCommentLengthRange: comment.range,
      galleryCommentPlaceholder: comment.placeholder,
      galleryCommentLabel: comment.label,
      galleryCommentRequired: comment.required,
    });

    this.galleryEnabled(data.galleryEnabled);

    let clarifyingQuestion = data.clarifyingQuestion;

    if (this.commentIsRequired() === null) {
      this.commentIsRequired(data.comment.required)
    }

    this.clarifyingQuestionEnabled(clarifyingQuestion.enabled);
    this.clarifyingQuestionForAllRates(clarifyingQuestion.forAllRates);
    this.clarifyingQuestionIsRequired(clarifyingQuestion.required);
    this.clarifyingQuestionText(clarifyingQuestion.text || "");
    this.clarifyingQuestionController.updateData({
      type: clarifyingQuestion.variantsType,
      variants: clarifyingQuestion.variants,
      customAnswerEnabled: clarifyingQuestion.customAnswerEnabled,
      customAnswerRange: clarifyingQuestion.customAnswerLengthRange,
      customAnswerPlaceholder: clarifyingQuestion.customAnswerPlaceholder,
      customAnswerLabel: clarifyingQuestion.customAnswerLabel,
    });

    let starsConfig = data.starsConfig;
    if (starsConfig) {
      this.color(starsConfig.color || starDefaultColor);
      this.count(starsConfig.count || 5);
      this.size(starsConfig.size || "md");

      let labels = starsConfig.labels || [];
      this.labels.forEach((label, index) => {
        let labelText = labels[index];
        label.value(labelText || "");
      });
      if (starsConfig.extraQuestionRateFrom && starsConfig.extraQuestionRateTo) {
        this.clarifyingQuestionForRates([starsConfig.extraQuestionRateFrom, starsConfig.extraQuestionRateTo]);
      } else {
        this.clarifyingQuestionForRates([1, starsConfig.count || 5]);
      }
    }

    this.skip(!!data.skip);
    this.skipText(data.skipText || "");

    this.showNumbers(!!data.showNumbers);
    this.showLabels(!!data.showLabels);

    if (data.ai_comment_processing) {
      const { sentiment, tagging } = data.ai_comment_processing;
      if (sentiment) {
        this.aiCommentSentimentEnabled(!!sentiment.enabled);
        this.aiCommentSentimentPrompt(sentiment.prompt || "");
      }
      if (tagging) {
        this.aiCommentTaggingEnabled(!!tagging.enabled);
        this.aiCommentTaggingPrompt(tagging.prompt || "");
        this.aiCommentTags(Array.isArray(tagging.tags) ? tagging.tags : []);
      }
    }
  }

  getData() {
    let data = super.getData();

    data.galleryEnabled = this.galleryEnabled();

    data.clarifyingQuestion = {
      enabled: this.clarifyingQuestionEnabled(),
      required: this.clarifyingQuestionIsRequired(),
      forAllRates: this.clarifyingQuestionForAllRates(),
      text: this.clarifyingQuestionText(),
      ...this.clarifyingQuestionController.getData(),
    };

    data.comment = {
      enabled: this.commentEnabled(),

      ...this.commentField.getData(),
      label: this.commentLabel(),
    };
    
    data.starsConfig = {
      color: this.color(),
      count: this.count(),
      size: this.size(),
      labels: this.visibleLabels().map((l) => l.value()),
    };

    data.skip = this.skip();
    data.skipText = this.skipText();

    data.showNumbers = this.showNumbers();
    data.showLabels = this.showLabels();

    if (
      this.clarifyingQuestionEnabled() &&
      !this.clarifyingQuestionForAllRates() &&
      this.clarifyingQuestionForRates()[0] &&
      this.clarifyingQuestionForRates()[1]
    ) {
      data.starsConfig['extra_question_rate_from'] = this.clarifyingQuestionForRates()[0];
      data.starsConfig['extra_question_rate_to'] = this.clarifyingQuestionForRates()[1];
    }

    let sentimentEnabled = !!this.aiCommentSentimentEnabled();
    let sentimentPrompt = (this.aiCommentSentimentPrompt() || "").trim();
    if (sentimentEnabled && !sentimentPrompt) sentimentEnabled = false;

    let taggingEnabled = !!this.aiCommentTaggingEnabled();
    let taggingPrompt = (this.aiCommentTaggingPrompt() || "").trim();
    let tags = this.aiCommentTags();
    if (taggingEnabled && (!taggingPrompt || !tags || !tags.length)) taggingEnabled = false;

    data.ai_comment_processing = {
      sentiment: {
        enabled: sentimentEnabled,
        prompt: sentimentEnabled ? sentimentPrompt : "",
      },
      tagging: {
        enabled: taggingEnabled,
        prompt: taggingEnabled ? taggingPrompt : "",
        tags: taggingEnabled ? tags : [],
      },
    };

    return data;
  }

  isValid() {
    if (!this.clarifyingQuestionText.isValid()) return false;

    if (this.clarifyingQuestionEnabled()) {
      if (!this.clarifyingQuestionController.isValid()) return false;
    }

    if (this.galleryEnabled()) {
      return super.isValid();
    }

    return this.defaultValidation();
  }

  openAiFieldProcessing() {
    const companyEnabled = this.companyAiEnabled && this.companyAiEnabled();
    const pollEnabled = this.pollAiEnabled && this.pollAiEnabled();

    if (!companyEnabled) {
      this.openDialog({
        name: "ai-processing-request-dialog",
        params: {
          url: "poll/send-ai-processing-request",
          view: 'request',
        },
      });
      return;
    }

    if (companyEnabled && !pollEnabled) {
      this.openDialog({
        name: "ai-processing-request-dialog",
        params: {
          view: 'direct-to-poll-settings',
          pollId: window.POLL && window.POLL.id,
        },
      });
      return;
    }

    this.openSidesheet({
      name: "ai-field-processing-sidesheet",
      params: {
        readonly: this.isFullBlocked || this.isBlocked(),
        showSentiment: true,
        showTagging: true,
        maxSentimentPromptLength: 2000,
        maxTaggingPromptLength: 4000,
        sentimentEnabled: this.aiCommentSentimentEnabled,
        sentimentPrompt: this.aiCommentSentimentPrompt,
        taggingEnabled: this.aiCommentTaggingEnabled,
        taggingPrompt: this.aiCommentTaggingPrompt,
        tags: this.aiCommentTags,
        questionId: this.id(),
        aiFieldId: 'stars_comment_tags',
        title: 'Обработка комментария с помощью ИИ',
      },
    });
  }

  openAiFieldCommentSidesheet() {
    this.openSidesheet({
      name: "ai-field-processing-sidesheet",
      params: {
        readonly: this.isFullBlocked || this.isBlocked(),
        showSentiment: true,
        showTagging: true,
        sentimentEnabled: this.aiCommentSentimentEnabled,
        sentimentPrompt: this.aiCommentSentimentPrompt,
        taggingEnabled: this.aiCommentTaggingEnabled,
        taggingPrompt: this.aiCommentTaggingPrompt,
        tags: this.aiCommentTags,
        questionId: this.id(),
        title: 'Обработка комментария с помощью ИИ',
      },
    });
  }
}

export default StarsQuestion;
