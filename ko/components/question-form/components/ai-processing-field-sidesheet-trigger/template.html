<div class="ai-processing-settings__button">
  <div class="ai-processing-button" data-bind="css: { 'ai-processing-button--active': isActive() }">
    <fc-button params="
      color: 'secondary',
      size: 'md',
      label: label,
      icon: { name: 'magic', size: 16 },
      click: handleClick
    "></fc-button>
    <!-- ko if: isActive() -->
    <div class="ai-processing-button__indicator">
      <div data-bind="component: { name: 'fc-icon', params: { name: 'check', size: 8, color: 'white' } }"></div>
    </div>
    <!-- /ko -->
  </div>
</div>
