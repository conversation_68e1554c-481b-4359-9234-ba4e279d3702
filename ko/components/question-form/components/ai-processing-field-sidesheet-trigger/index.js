import html from './template.html';
import './style.less';
import { DialogsModule } from '@/utils/dialogs-module';
import '@/dialogs/ai-processing-request-dialog';

ko.components.register('ai-processing-field-sidesheet-trigger', {
  viewModel: function(params) {
    DialogsModule(this);
    this.label = params.label || 'Тональность и теги через модуль ИИ';
    this.onSidesheetTrigger = typeof params.onSidesheetTrigger === 'function' ? params.onSidesheetTrigger : function() {};

    this.companyAiEnabled = params.companyAiEnabled;
    this.pollAiEnabled = params.pollAiEnabled;

    this.pollId = params.pollId;

    if (ko.isObservable && ko.isObservable(params.isActive)) {
      this.isActive = params.isActive;
    } else if (typeof params.isActive === 'function' && params.isActive.peek) {
      this.isActive = params.isActive;
    } else {
      this.isActive = ko.observable(!!params.isActive);
    }

    const unwrap = (v) => (ko && ko.isObservable && ko.isObservable(v) ? v() : v);

    this.handleClick = () => {
      const companyEnabled = !!unwrap(this.companyAiEnabled);
      const pollEnabled = !!unwrap(this.pollAiEnabled);

      if (!companyEnabled) {
        this.openDialog({
          name: 'ai-processing-request-dialog',
          params: { url: 'poll/send-ai-processing-request', view: 'request' },
        });
        return;
      }
      if (companyEnabled && !pollEnabled) {
        const pollId = this.pollId || (window.POLL && window.POLL.id);
        this.openDialog({
          name: 'ai-processing-request-dialog',
          params: { view: 'direct-to-poll-settings', pollId },
        });
        return;
      }
      this.onSidesheetTrigger();
    };
  },
  template: html,
});
