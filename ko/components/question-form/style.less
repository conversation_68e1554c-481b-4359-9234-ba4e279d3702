@import 'Style/colors';

.link-field-block {
  .foquz-switch {
    margin-bottom: 24px;
  }
  .foquz-checkbox {
    height: 48px;
    display: flex;
    align-items: center;

    .f-check-label {
      &:before {
        top: 0;
        bottom: 0;
        margin-top: auto;
        margin-bottom: auto;
      }
      &:after {
        top: 0;
        bottom: 0;
        margin-top: auto;
        margin-bottom: auto;
      }
    }
  }
}

.link-field-wrapper + hr {
  margin-top: 0;
}

.question-form--blocked {
  .media-variants-controller .variants-controller__variant {
    &:last-child {
      border-bottom: 0;
    }
  }

  .survey-question__quiz-control-list-content {
    margin-bottom: 0;
  }

  .survey-question__quiz-control-list-item {
    &:last-child {
      border-bottom: 0;
    }
  }
}

.question-type-select.select2-container {
  .select2-results {
    .select2-results__options {
      padding-top: 55px;
      position: relative;
      max-height: 400px !important;
    }
    .select2-results__option {
      &[role='group'] {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        width: 100%;
        padding: 0!important;

        .select2-results__group {
          display: none;
        }

        .select2-results__options {
          display: flex;
          flex-direction: row;
          padding-top: 0;
          flex-wrap: nowrap;

          &:after {
            content: '';
            position: absolute;
            left: 10px;
            right: 10px;
            bottom: -8px;
            border-bottom: 1px solid @f-color-border;
          }
        }
      }

      &[aria-disabled="true"] {
        .f-icon {
          color: @f-color-service;
        }
      }
    }
  }
}

.poll-question-form__source {
  .hat-radio-group__radio {
    width: 50%;
  }
}

.variants-controller__children-container {
  margin-top: 13px;
  margin-left: 40px;
}

.emty-variant-controller .row {
  flex: 1;
}
.without-points-info-text {
  font-size: 12px;
  margin-bottom: 10px;
  color: #73808D;
}

.subdescription-text-editor .ckeditor:not(.ckeditor--inter) .ck-content {
  min-height: 48px !important;
}

// Import First Click Test styles
@import './models/types/first-click-test/styles';