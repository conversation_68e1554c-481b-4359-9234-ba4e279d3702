import { copyToClipboard } from '@/utils/copy-to-clipboard';
import { Translator } from '@/utils/translate';

const MainTranslator = Translator('main');
let __uid = 1;

export class ViewModel {
  constructor(params = {}, element) {
    this.element = element;

    this.value = params.value || ko.observable('');
    this.disabled = params.disabled || false;
    this.readonly = params.readonly || false;
    this.placeholder = params.placeholder || '';
    this.maxlength = params.maxlength || undefined;
    this.maxHeight = params.maxHeight || undefined;
    this.isInvalid = params.isInvalid || false;

    this.copyable = params.copyable || false;
    this.tooltip = params.tooltip || MainTranslator.t('Скопировать в буфер');
    this.copiedText = params.copiedText || MainTranslator.t('Скопировано в буфер');
    this.targetId = `scrollable-textarea-${__uid++}`;
    this.copied = ko.observable(false);

    this.hasShadowTop = ko.observable(false);
    this.hasShadowBottom = ko.observable(false);
    this.hasScrollbar = ko.observable(false);
    this.hasCustomScrollbar = ko.observable(false);

    this.subscriptions = [];

    if (ko.isObservable(this.value)) {
      this.subscriptions.push(
        this.value.subscribe(() => setTimeout(() => this.updateShadows(), 0))
      );
    }

    setTimeout(() => this.updateShadows(), 0);

    this._onResize = () => this.updateShadows();
    window.addEventListener('resize', this._onResize);
  }

  getTextarea() {
    return this.element.querySelector('textarea');
  }

  onScroll() {
    this.updateShadows();
  }

  updateShadows() {
    const el = this.getTextarea();
    if (!el) return;
    const st = el.scrollTop;
    const ch = el.clientHeight;
    const sh = el.scrollHeight;
    this.hasShadowTop(st > 0);
    this.hasShadowBottom(st + ch < sh);
    this.hasScrollbar(sh > ch);

    const width = Math.max(0, Math.round(el.offsetWidth - el.clientWidth));
    this.element.style.setProperty('--fc-scrollable-area-scrollbar-width', width + 'px');

    const isCustomScrollbar = width === 4 && this.hasScrollbar();

    if (isCustomScrollbar && this.element) {
      this.element.classList.add('scrollable-textarea--custom-scrollbar');
      this.hasCustomScrollbar(isCustomScrollbar);
    } else {
      this.element.classList.remove('scrollable-textarea--custom-scrollbar');
      this.hasCustomScrollbar(isCustomScrollbar);
    }
  }

  copy() {
    copyToClipboard(ko.toJS(this.value), this.element).then(() => {
      this.copied(true);
    });
  }

  dispose() {
    this.subscriptions.forEach((s) => s.dispose());
    window.removeEventListener('resize', this._onResize);
  }
}
